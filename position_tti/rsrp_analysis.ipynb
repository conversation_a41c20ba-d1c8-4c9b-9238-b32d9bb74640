import os,sys
sys.path.append(os.getcwd())
from func.BM_functions import *
from func.system_functions import *
from func.process import *
from func.setsumi_env1 import setsumi_env1
from utils import *
import torch
import pickle
from train_td3 import seed_torch,parse_args,train,infer

path = os.getcwd()

path ='/home/<USER>/quadriga/inf_v0/'
seed = 777
seed_torch(seed)
env = setsumi_env1(path=path, seed=seed, max_length=4000,mu=0.9,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False,rsrp_source="pg_all")  


sim_par = Sim_Log(path)
nUE = sim_par.nUE
print(nUE)
UE=env.nUE
print(UE)

rsrp=env.rsrp

import matplotlib.pyplot as plt
import numpy as np

# 假设已经获得了rsrp数据：rsrp = env.rsrp
# rsrp的维度是[10, 100, 3]，其中10是用户个数，100是时间点，3是rsrp值

# 对第三个维度进行平均
rsrp_avg = np.mean(rsrp, axis=2)  # 维度变为[10, 100]

# 再对所有用户进行平均，得到每个时间点的平均RSRP值
rsrp_time_avg = np.mean(rsrp_avg, axis=0)  # 维度变为[100]

# 创建时间点数组
time_points = np.arange(100)

# 绘制折线图
plt.figure(figsize=(12, 6))
plt.plot(time_points, rsrp_time_avg, linewidth=2, marker='o', markersize=3)
plt.xlabel('time')
plt.ylabel('rsrp')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
plt.savefig("rsrp1.png")
# 如果需要为每个用户单独绘制折线图
plt.figure(figsize=(12, 8))
for user_id in range(10):
    plt.plot(time_points, rsrp_avg[user_id], label=f'user{user_id+1}', alpha=0.7)

plt.xlabel('time')
plt.ylabel('rsrp')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
plt.savefig("rsrp2.png")

print(rsrp.shape)
grouped = rsrp.reshape(10, 10, 10, 3)

# 2. 直接计算算术平均（不加权）
result = np.mean(grouped, axis=2)  # 沿时间点维度求平均

# 结果形状: (10, 10, 1)
print(result.shape)  # 输出: (10, 10, 1)

import numpy as np
import matplotlib.pyplot as plt

def plot_rsrp_vs_time(rsrp_data, time_interval=2):
    """
    绘制每个用户的RSRP随时间变化图
    
    Parameters:
    rsrp_data: shape (n_users, n_time, 1) - RSRP数据
    time_interval: 时间间隔，单位秒
    """
    n_users, n_time, _ = rsrp_data.shape
    
    # 创建时间轴 (秒)
    time_axis = np.arange(n_time) * time_interval
    
    # 提取RSRP值 (去掉最后一个维度)
    rsrp_values = rsrp_data.squeeze(axis=-1)  # shape: (n_users, n_time)
    
    # 创建子图 - 5行2列布局
    fig, axes = plt.subplots(5, 2, figsize=(15, 20))
    axes = axes.flatten()  # 将2D数组展平为1D
    
    # 生成不同颜色
    colors = plt.cm.tab10(np.linspace(0, 1, n_users))
    
    # 为每个用户绘制RSRP曲线
    for user_idx in range(n_users):
        ax = axes[user_idx]
        
        # 绘制RSRP曲线
        ax.plot(time_axis, rsrp_values[user_idx, :], 
               color=colors[user_idx], linewidth=2, marker='o', markersize=4,
               label=f'User {user_idx + 1}')
        
        # 设置标题和标签
        ax.set_title(f'User {user_idx + 1} RSRP vs Time', fontsize=12, fontweight='bold')
        ax.set_xlabel('Time (seconds)', fontsize=10)
        ax.set_ylabel('RSRP (dBm)', fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 添加统计信息到图上
        mean_rsrp = np.mean(rsrp_values[user_idx, :])
        min_rsrp = np.min(rsrp_values[user_idx, :])
        max_rsrp = np.max(rsrp_values[user_idx, :])
        
        # 在图上显示统计信息
        stats_text = f'Mean: {mean_rsrp:.1f} dBm\nMin: {min_rsrp:.1f} dBm\nMax: {max_rsrp:.1f} dBm'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=8,
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.suptitle('RSRP vs Time for All Users', fontsize=16, fontweight='bold', y=0.995)
    return fig, axes

def plot_all_users_combined(rsrp_data, time_interval=2):
    """
    在一张图上显示所有用户的RSRP曲线
    """
    n_users, n_time, _ = rsrp_data.shape
    
    # 创建时间轴
    time_axis = np.arange(n_time) * time_interval
    rsrp_values = rsrp_data.squeeze(axis=-1)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 生成不同颜色和线型
    colors = plt.cm.tab10(np.linspace(0, 1, n_users))
    line_styles = ['-', '--', '-.', ':', '-', '--', '-.', ':', '-', '--']
    
    # 绘制所有用户的曲线
    for user_idx in range(n_users):
        ax.plot(time_axis, rsrp_values[user_idx, :], 
               color=colors[user_idx], 
               linestyle=line_styles[user_idx % len(line_styles)],
               linewidth=2, marker='o', markersize=3, alpha=0.8,
               label=f'User {user_idx + 1}')
    
    # 设置图形属性
    ax.set_title('RSRP vs Time - All Users Combined', fontsize=14, fontweight='bold')
    ax.set_xlabel('Time (seconds)', fontsize=12)
    ax.set_ylabel('RSRP (dBm)', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    return fig, ax

def plot_rsrp_statistics(rsrp_data, time_interval=2):
    """
    绘制RSRP统计图：平均值、最大值、最小值随时间变化
    """
    n_users, n_time, _ = rsrp_data.shape
    time_axis = np.arange(n_time) * time_interval
    rsrp_values = rsrp_data.squeeze(axis=-1)
    
    # 计算统计量
    mean_rsrp = np.mean(rsrp_values, axis=0)  # 所有用户的平均值
    max_rsrp = np.max(rsrp_values, axis=0)    # 所有用户的最大值
    min_rsrp = np.min(rsrp_values, axis=0)    # 所有用户的最小值
    std_rsrp = np.std(rsrp_values, axis=0)    # 标准差
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 绘制统计曲线
    ax.plot(time_axis, mean_rsrp, 'b-', linewidth=3, label='Mean RSRP', marker='o')
    ax.plot(time_axis, max_rsrp, 'g-', linewidth=2, label='Max RSRP', marker='^')
    ax.plot(time_axis, min_rsrp, 'r-', linewidth=2, label='Min RSRP', marker='v')
    
    # 添加标准差阴影区域
    ax.fill_between(time_axis, 
                   mean_rsrp - std_rsrp, mean_rsrp + std_rsrp,
                   alpha=0.3, color='blue', label='±1 Std Dev')
    
    # 设置图形属性
    ax.set_title('RSRP Statistics Over Time', fontsize=14, fontweight='bold')
    ax.set_xlabel('Time (seconds)', fontsize=12)
    ax.set_ylabel('RSRP (dBm)', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    plt.tight_layout()
    return fig, ax

def print_rsrp_summary(rsrp_data, time_interval=2):
    """
    打印RSRP数据的详细统计信息
    """
    n_users, n_time, _ = rsrp_data.shape
    rsrp_values = rsrp_data.squeeze(axis=-1)
    
    print("="*60)
    print("RSRP数据统计摘要")
    print("="*60)
    print(f"用户数量: {n_users}")
    print(f"时间步数: {n_time}")
    print(f"时间间隔: {time_interval} 秒")
    print(f"总时长: {(n_time-1) * time_interval} 秒")
    
    print("\n各用户RSRP统计:")
    print("-"*60)
    print("用户\t平均值\t最小值\t最大值\t标准差\t范围")
    print("-"*60)
    
    for user_idx in range(n_users):
        user_rsrp = rsrp_values[user_idx, :]
        mean_val = np.mean(user_rsrp)
        min_val = np.min(user_rsrp)
        max_val = np.max(user_rsrp)
        std_val = np.std(user_rsrp)
        range_val = max_val - min_val
        
        print(f"User{user_idx+1:2d}\t{mean_val:6.1f}\t{min_val:6.1f}\t{max_val:6.1f}\t{std_val:6.1f}\t{range_val:6.1f}")
    
    # 整体统计
    overall_mean = np.mean(rsrp_values)
    overall_min = np.min(rsrp_values)
    overall_max = np.max(rsrp_values)
    overall_std = np.std(rsrp_values)
    
    print("-"*60)
    print(f"整体\t{overall_mean:6.1f}\t{overall_min:6.1f}\t{overall_max:6.1f}\t{overall_std:6.1f}\t{overall_max-overall_min:6.1f}")
    print("="*60)

fig2, ax2 = plot_all_users_combined(rsrp, time_interval=2)
plt.show()
plt.savefig("rsrp2.png")

# 4. 打印统计信息
print_rsrp_summary(rsrp, time_interval=2)