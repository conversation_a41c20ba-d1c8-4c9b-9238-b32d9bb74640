#!/usr/bin/env python3
"""
Quadriga数据集完整训练流程
包含数据预处理、模型训练和评估
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import time
import os
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scipy，如果没有则提供替代方案
try:
    from scipy import stats
    SCIPY_AVAILABLE = True
except ImportError:
    print("警告: scipy库未安装，某些统计功能将被禁用")
    SCIPY_AVAILABLE = False

# 假设complexPyTorch库已安装
try:
    from complexPyTorch.complexLayers import ComplexBatchNorm2d, ComplexConv2d, ComplexLinear
    from complexPyTorch.complexFunctions import complex_relu, complex_max_pool2d
except ImportError:
    print("警告: complexPyTorch库未安装，将使用模拟的复数层")
    
    # 模拟复数层定义 (简化版本)
    class ComplexLinear(nn.Module):
        def __init__(self, in_features, out_features):
            super().__init__()
            self.real_linear = nn.Linear(in_features, out_features)
            self.imag_linear = nn.Linear(in_features, out_features)
            
        def forward(self, x):
            real_out = self.real_linear(x.real) - self.imag_linear(x.imag)
            imag_out = self.real_linear(x.imag) + self.imag_linear(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexConv2d(nn.Module):
        def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0):
            super().__init__()
            self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            
        def forward(self, x):
            real_out = self.real_conv(x.real) - self.imag_conv(x.imag)
            imag_out = self.real_conv(x.imag) + self.imag_conv(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexBatchNorm2d(nn.Module):
        def __init__(self, num_features):
            super().__init__()
            self.real_bn = nn.BatchNorm2d(num_features)
            self.imag_bn = nn.BatchNorm2d(num_features)
            
        def forward(self, x):
            return torch.complex(self.real_bn(x.real), self.imag_bn(x.imag))
    
    def complex_relu(x):
        return torch.complex(torch.relu(x.real), torch.relu(x.imag))

# 模型定义
class Flatten(nn.Module):
    def forward(self, input):
        return input.view(input.size(0), -1)

class HH2ComplexMLP(nn.Module):
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.flatten = Flatten()
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        self.mlp_5 = ComplexLinear(input_size // 8, n_outputs)
        
    def forward(self, x):
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_5(x)
        # 将复数角度从-π到π映射到-180到180度
        output = (180/np.pi) * x.angle()
        return output

class HH2ComplexMLPClassifier(nn.Module):
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.flatten = Flatten()
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        self.mlp_5 = ComplexLinear(input_size // 8, n_outputs)
        
    def forward(self, x):
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_5(x)
        output = x.abs()
        return output

class HH2ComplexCONV_1(nn.Module):
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.flatten = Flatten()
        self.conv_1 = ComplexConv2d(1, 1, kernel_size=(3,3), stride=1, padding=2)
        self.conv_2 = ComplexConv2d(1, 1, kernel_size=(3,3), stride=1, padding=2)
        self.bn_1 = ComplexBatchNorm2d(1)
        self.bn_2 = ComplexBatchNorm2d(1)
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        self.mlp_4 = ComplexLinear(input_size // 8, n_outputs)
        
    def forward(self, x):
        x = self.conv_1(x)
        x = self.bn_1(x)
        x = complex_relu(x)
        
        x = self.conv_2(x)
        x = self.bn_2(x)
        x = complex_relu(x)
        x = x.view(x.size(0), -1)
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_4(x)
        # 将复数角度从-π到π映射到-180到180度
        output = (180/np.pi) * x.angle()
        return output

# 数据预处理类
class QuadrigaDataPreprocessor:
    def __init__(self, data_path, H_ants=8, V_ants=4, nSub=17, no_snapshots=1):
        self.data_path = data_path
        self.H_ants = H_ants
        self.V_ants = V_ants
        self.nSub = nSub
        self.no_snapshots = no_snapshots
        self.UE_port = 4  # UE天线端口数
        self.BS_port = H_ants * V_ants * 2  # BS天线端口数
        
    def load_channel_data(self, filename='H_freq.txt'):
        """加载信道数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"数据文件不存在: {file_path}")
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        complex_data_list = []
        
        for line in lines:
            if line.strip():
                values = list(map(float, line.strip().split()))
                values = np.array(values)
                
                # 重塑为实部虚部对
                real_imag_pairs = values.reshape(2, -1)
                complex_values = real_imag_pairs[0] + 1j * real_imag_pairs[1]
                
                # 重塑为信道矩阵
                if filename == 'H_freq.txt':
                    channel_matrix = complex_values.reshape(self.BS_port, self.UE_port, self.nSub)
                else:
                    channel_matrix = complex_values.reshape(self.BS_port, self.UE_port, self.no_snapshots)
                
                complex_data_list.append(channel_matrix)
        
        return np.array(complex_data_list)
    
    def load_location_data(self, filename='loc.txt'):
        """加载UE位置数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            print(f"警告: 位置文件不存在: {file_path}")
            return None
        
        locations = []
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    coords = list(map(float, line.strip().split()))
                    locations.append(coords[:3])
        
        return np.array(locations)
    
    def load_path_gain_data(self, filename='pg.txt'):
        """加载路径增益数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            print(f"警告: 路径增益文件不存在: {file_path}")
            return None
        
        path_gains = []
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    pg_value = float(line.strip())
                    path_gains.append(pg_value)
        
        return np.array(path_gains)
    
    def create_covariance_matrix(self, channel_data):
        """创建协方差矩阵用于CNN"""
        N_samples = channel_data.shape[0]
        N_freq_time = channel_data.shape[3] if len(channel_data.shape) == 4 else 1
        
        if len(channel_data.shape) == 3:
            channel_data = channel_data[:, :, :, np.newaxis]
            N_freq_time = 1
            
        cov_matrices = np.zeros((N_samples, N_freq_time, self.BS_port, self.BS_port), dtype=complex)
        
        for sample_idx in range(N_samples):
            for freq_idx in range(N_freq_time):
                H = channel_data[sample_idx, :, :, freq_idx]
                cov_matrices[sample_idx, freq_idx] = np.dot(H, H.conj().T)
        
        return cov_matrices
    
    def normalize_data(self, data, method='standardize'):
        """数据归一化"""
        if data is None:
            return None, None
            
        original_shape = data.shape
        
        if np.iscomplexobj(data):
            real_part = data.real.reshape(-1, 1)
            imag_part = data.imag.reshape(-1, 1)
            
            if method == 'standardize':
                scaler_real = StandardScaler()
                scaler_imag = StandardScaler()
            else:
                scaler_real = MinMaxScaler()
                scaler_imag = MinMaxScaler()
            
            real_normalized = scaler_real.fit_transform(real_part)
            imag_normalized = scaler_imag.fit_transform(imag_part)
            
            normalized_data = (real_normalized + 1j * imag_normalized).reshape(original_shape)
            scaler = (scaler_real, scaler_imag)
        else:
            data_reshaped = data.reshape(-1, 1)
            if method == 'standardize':
                scaler = StandardScaler()
            else:
                scaler = MinMaxScaler()
            
            normalized_data = scaler.fit_transform(data_reshaped).reshape(original_shape)
        
        return normalized_data, scaler

class QuadrigaDataset(Dataset):
    def __init__(self, channel_data, labels=None, locations=None, path_gains=None, 
                 model_type='MLP', transform=None):
        self.channel_data = torch.tensor(channel_data, dtype=torch.complex64)
        self.labels = torch.tensor(labels, dtype=torch.float32) if labels is not None else None
        self.locations = torch.tensor(locations, dtype=torch.float32) if locations is not None else None
        self.path_gains = torch.tensor(path_gains, dtype=torch.float32) if path_gains is not None else None
        self.model_type = model_type
        self.transform = transform
        
    def __len__(self):
        return len(self.channel_data)
    
    def __getitem__(self, idx):
        channel = self.channel_data[idx]
        
        if self.model_type == 'MLP':
            channel = channel.flatten()
        elif self.model_type == 'CNN':
            if len(channel.shape) == 2:
                channel = channel.unsqueeze(0)
        
        if self.transform:
            channel = self.transform(channel)
        
        sample = {'channel': channel}
        
        if self.labels is not None:
            sample['label'] = self.labels[idx]
        if self.locations is not None:
            sample['location'] = self.locations[idx]
        if self.path_gains is not None:
            sample['path_gain'] = self.path_gains[idx]
            
        return sample

class QuadrigaTrainer:
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        self.val_maes = []
        self.val_stds = []  # 添加存储MAE标准差的列表
        
    def train_epoch(self, train_loader, optimizer, criterion, epoch):
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1} Training')
        
        for batch in progress_bar:
            channel_data = batch['channel'].to(self.device)
            labels = batch['label'].to(self.device)
            
            optimizer.zero_grad()
            outputs = self.model(channel_data)
            
            if outputs.dim() > 1:
                outputs = outputs.squeeze()
            loss = criterion(outputs, labels)
            
            loss.backward()
            
            # 梯度裁剪防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            progress_bar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        return total_loss / num_batches
    
    def validate_epoch(self, val_loader, criterion):
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        batch_maes = []  # 存储每个批次的MAE
        num_batches = 0

        with torch.no_grad():
            for batch in val_loader:
                channel_data = batch['channel'].to(self.device)
                labels = batch['label'].to(self.device)

                outputs = self.model(channel_data)
                if outputs.dim() > 1:
                    outputs = outputs.squeeze()

                loss = criterion(outputs, labels)
                total_loss += loss.item()
                num_batches += 1

                # 转换为numpy数组
                batch_predictions = outputs.cpu().numpy()
                batch_labels = labels.cpu().numpy()

                # 计算当前批次的MAE
                batch_mae = mean_absolute_error(batch_labels, batch_predictions)
                batch_maes.append(batch_mae)

                all_predictions.extend(batch_predictions)
                all_labels.extend(batch_labels)

        avg_loss = total_loss / num_batches
        mae = mean_absolute_error(all_labels, all_predictions)
        rmse = np.sqrt(mean_squared_error(all_labels, all_predictions))

        # 计算MAE的标准差
        mae_std = np.std(batch_maes) if len(batch_maes) > 1 else 0.0

        return avg_loss, mae, rmse, mae_std, all_predictions, all_labels
    
    def train(self, train_loader, val_loader, num_epochs=100, learning_rate=0.001, 
              patience=10, save_path='best_model.pth'):
        
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
        
        best_mae = float('inf')
        patience_counter = 0
        
        print(f"开始训练，设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss = self.train_epoch(train_loader, optimizer, criterion, epoch)
            
            # 验证
            val_loss, val_mae, val_rmse, mae_std, predictions, labels = self.validate_epoch(val_loader, criterion)

            # 学习率调度
            scheduler.step(val_mae)

            # 记录指标
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_maes.append(val_mae)
            self.val_stds.append(mae_std)  # 记录MAE标准差
            
            epoch_time = time.time() - start_time
            
            # 打印信息
            print(f'Epoch [{epoch+1:3d}/{num_epochs}] ({epoch_time:.1f}s) | '
                  f'Train: {train_loss:.6f} | Val: {val_loss:.6f} | '
                  f'MAE: {val_mae:.6f} ± {mae_std:.6f} | RMSE: {val_rmse:.6f} | '
                  f'LR: {optimizer.param_groups[0]["lr"]:.1e}')
            
            # 早停和模型保存
            if val_mae < best_mae:
                best_mae = val_mae
                patience_counter = 0
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_mae': best_mae,
                    'train_losses': self.train_losses,
                    'val_losses': self.val_losses,
                    'val_maes': self.val_maes,
                    'val_stds': self.val_stds  # 保存MAE标准差
                }, save_path)
                print(f'  ✓ 新的最佳MAE! 模型已保存')
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                print(f'\n早停触发，已连续{patience}个epoch无改善')
                break
        
        return best_mae

def preprocess_quadriga_data(data_path, model_type='MLP', use_covariance=False, 
                           normalize=True, test_split=0.2, batch_size=32):
    """完整的数据预处理流程"""
    
    print("="*60)
    print("开始Quadriga数据预处理")
    print("="*60)
    
    # 初始化预处理器
    preprocessor = QuadrigaDataPreprocessor(data_path)
    
    try:
        # 加载数据
        print("📁 加载信道数据...")
        channel_data = preprocessor.load_channel_data('H_freq.txt')
        print(f"   ✓ 信道数据形状: {channel_data.shape}")
        
        print("📍 加载位置数据...")
        locations = preprocessor.load_location_data('loc.txt')
        if locations is not None:
            print(f"   ✓ 位置数据形状: {locations.shape}")
        
        print("📊 加载路径增益数据...")
        path_gains = preprocessor.load_path_gain_data('pg.txt')
        if path_gains is not None:
            print(f"   ✓ 路径增益数据形状: {path_gains.shape}")
        
    except FileNotFoundError as e:
        print(f"❌ 文件加载错误: {e}")
        print("使用模拟数据进行演示...")
        return create_dummy_data(batch_size)
    
    # 为CNN创建协方差矩阵
    if model_type == 'CNN' and use_covariance:
        print("🔄 创建协方差矩阵...")
        channel_data = preprocessor.create_covariance_matrix(channel_data)
        print(f"   ✓ 协方差矩阵形状: {channel_data.shape}")
    
    # 数据归一化
    if normalize:
        print("📏 数据归一化...")
        channel_data, channel_scaler = preprocessor.normalize_data(channel_data)
        locations, loc_scaler = preprocessor.normalize_data(locations, method='minmax') if locations is not None else (None, None)
        path_gains, pg_scaler = preprocessor.normalize_data(path_gains) if path_gains is not None else (None, None)
        print("   ✓ 归一化完成")
    
    # 创建标签
    print("🏷️  创建训练标签...")
    if locations is not None:
        # 使用位置计算角度作为回归目标
        angles = np.arctan2(locations[:, 1], locations[:, 0]) * 180 / np.pi
        labels = angles
        print(f"   ✓ 角度标签范围: [{labels.min():.1f}°, {labels.max():.1f}°]")
    else:
        # 使用模拟标签
        labels = np.random.uniform(-180, 180, len(channel_data))
        print("   ✓ 使用模拟角度标签")
    
    # 数据集分割
    print("✂️  数据集分割...")
    n_samples = len(channel_data)
    indices = np.random.permutation(n_samples)
    split_idx = int(n_samples * (1 - test_split))
    
    train_indices = indices[:split_idx]
    val_indices = indices[split_idx:]
    
    print(f"   ✓ 训练集: {len(train_indices)} 样本")
    print(f"   ✓ 验证集: {len(val_indices)} 样本")
    
    # 创建数据集
    train_dataset = QuadrigaDataset(
        channel_data[train_indices],
        labels[train_indices],
        locations[train_indices] if locations is not None else None,
        path_gains[train_indices] if path_gains is not None else None,
        model_type=model_type
    )
    
    val_dataset = QuadrigaDataset(
        channel_data[val_indices], 
        labels[val_indices],
        locations[val_indices] if locations is not None else None,
        path_gains[val_indices] if path_gains is not None else None,
        model_type=model_type
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    # 计算输入尺寸
    sample_batch = next(iter(train_loader))
    input_size = sample_batch['channel'].shape[1:]
    if model_type == 'MLP':
        input_size = np.prod(input_size)
    
    data_info = {
        'input_size': input_size,
        'n_samples': n_samples,
        'n_train': len(train_indices),
        'n_val': len(val_indices),
        'channel_shape': channel_data.shape,
        'label_range': [labels.min(), labels.max()],
        'scalers': {
            'channel': channel_scaler if normalize else None,
            'location': loc_scaler if normalize and locations is not None else None,
            'path_gain': pg_scaler if normalize and path_gains is not None else None
        }
    }
    
    print("✅ 数据预处理完成!")
    return train_loader, val_loader, data_info

def create_dummy_data(batch_size=32):
    """创建模拟数据"""
    print("🎭 创建模拟数据用于演示...")
    
    n_samples = 2000
    H_ants, V_ants = 8, 4
    UE_port = 4
    nSub = 17
    BS_port = H_ants * V_ants * 2
    
    # 创建具有现实性的模拟信道数据
    channel_data = (np.random.randn(n_samples, BS_port, UE_port, nSub) + 
                   1j * np.random.randn(n_samples, BS_port, UE_port, nSub)) * 0.1
    
    # 创建相关的位置和角度数据
    angles_true = np.random.uniform(-180, 180, n_samples)
    locations = np.column_stack([
        np.cos(angles_true * np.pi / 180) * np.random.uniform(10, 50, n_samples),
        np.sin(angles_true * np.pi / 180) * np.random.uniform(10, 50, n_samples),
        np.random.uniform(1, 3, n_samples)
    ])
    
    # 添加噪声到角度标签
    labels = angles_true + np.random.normal(0, 2, n_samples)
    
    # 数据集分割
    train_size = int(0.8 * n_samples)
    
    train_dataset = QuadrigaDataset(
        channel_data[:train_size],
        labels[:train_size],
        locations[:train_size],
        model_type='MLP'
    )
    
    val_dataset = QuadrigaDataset(
        channel_data[train_size:],
        labels[train_size:], 
        locations[train_size:],
        model_type='MLP'
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    data_info = {
        'input_size': BS_port * UE_port * nSub,
        'n_train': train_size,
        'n_val': n_samples - train_size,
        'channel_shape': channel_data.shape,
        'label_range': [labels.min(), labels.max()]
    }
    
    print(f"   ✓ 模拟数据创建完成，形状: {channel_data.shape}")
    return train_loader, val_loader, data_info

def evaluate_model(model, test_loader, device, model_save_path=None):
    """模型评估"""
    if model_save_path and os.path.exists(model_save_path):
        checkpoint = torch.load(model_save_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 已加载最佳模型 (训练轮数: {checkpoint['epoch']+1})")
    
    model.eval()
    all_predictions = []
    all_labels = []
    batch_maes = []  # 存储每个批次的MAE

    print("🔍 开始最终评估...")
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            channel_data = batch['channel'].to(device)
            labels = batch['label'].to(device)

            outputs = model(channel_data)
            if outputs.dim() > 1:
                outputs = outputs.squeeze()

            # 转换为numpy数组
            batch_predictions = outputs.cpu().numpy()
            batch_labels = labels.cpu().numpy()

            # 计算当前批次的MAE
            batch_mae = mean_absolute_error(batch_labels, batch_predictions)
            batch_maes.append(batch_mae)

            all_predictions.extend(batch_predictions)
            all_labels.extend(batch_labels)

    predictions = np.array(all_predictions)
    labels = np.array(all_labels)

    # 计算详细指标
    mae = mean_absolute_error(labels, predictions)
    mse = mean_squared_error(labels, predictions)
    rmse = np.sqrt(mse)
    residuals = predictions - labels
    max_error = np.max(np.abs(residuals))
    std_error = np.std(residuals)

    # 计算MAE的标准差
    mae_std = np.std(batch_maes) if len(batch_maes) > 1 else 0.0
    
    # 计算分位数误差
    percentiles = [50, 75, 90, 95, 99]
    abs_errors = np.abs(residuals)
    percentile_errors = [np.percentile(abs_errors, p) for p in percentiles]
    
    metrics = {
        'MAE': mae,
        'MAE_STD': mae_std,  # 添加MAE的标准差
        'MSE': mse,
        'RMSE': rmse,
        'Max_Error': max_error,
        'Std_Error': std_error,
        'Predictions': predictions,
        'Labels': labels,
        'Residuals': residuals,
        'Percentile_Errors': dict(zip(percentiles, percentile_errors))
    }
    
    return metrics

def print_final_results(metrics):
    """打印最终结果"""
    print("\n" + "="*70)
    print("🎯 最终模型评估结果")
    print("="*70)
    print(f"📊 核心指标:")
    print(f"   MAE (平均绝对误差):     {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}")
    print(f"   MSE (均方误差):         {metrics['MSE']:.6f}")
    print(f"   RMSE (均方根误差):      {metrics['RMSE']:.6f}")
    print(f"   最大误差:              {metrics['Max_Error']:.6f}")
    print(f"   误差标准差:            {metrics['Std_Error']:.6f}")
    
    print(f"\n📈 误差分位数:")
    for percentile, error in metrics['Percentile_Errors'].items():
        print(f"   {percentile:2d}% 误差:              {error:.6f}")
    
    print(f"\n📋 数据统计:")
    print(f"   预测值范围:            [{metrics['Predictions'].min():.3f}, {metrics['Predictions'].max():.3f}]")
    print(f"   真实值范围:            [{metrics['Labels'].min():.3f}, {metrics['Labels'].max():.3f}]")
    print(f"   样本数量:              {len(metrics['Labels'])}")
    
    print("="*70)
    print(f"🏆 最终MAE结果: {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}")
    print("="*70)

def plot_results(trainer, metrics, save_dir='./results/'):
    """绘制训练和评估结果，包含标准差误差棒"""
    os.makedirs(save_dir, exist_ok=True)

    # 1. 训练曲线
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))

    epochs = range(1, len(trainer.train_losses) + 1)

    # 损失曲线
    axes[0].plot(epochs, trainer.train_losses, 'b-', label='Train Loss', linewidth=2)
    axes[0].plot(epochs, trainer.val_losses, 'r-', label='Val Loss', linewidth=2)
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training and Validation Loss')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # MAE曲线（带误差棒）
    if hasattr(trainer, 'val_stds') and len(trainer.val_stds) > 0:
        axes[1].errorbar(epochs, trainer.val_maes, yerr=trainer.val_stds,
                        fmt='g-', label='Val MAE ± STD', linewidth=2,
                        capsize=3, capthick=1, alpha=0.8)
    else:
        axes[1].plot(epochs, trainer.val_maes, 'g-', label='Val MAE', linewidth=2)
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('MAE')
    axes[1].set_title('Validation MAE with Standard Deviation')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    # 最终MAE高亮
    best_epoch = np.argmin(trainer.val_maes) + 1
    best_mae = min(trainer.val_maes)
    axes[1].plot(best_epoch, best_mae, 'ro', markersize=8, label=f'Best MAE: {best_mae:.4f}')
    axes[1].legend()

    # 误差分布
    axes[2].hist(np.abs(metrics['Residuals']), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[2].axvline(x=metrics['MAE'], color='red', linestyle='--', linewidth=2, label=f'MAE: {metrics["MAE"]:.4f}')
    axes[2].set_xlabel('Absolute Error')
    axes[2].set_ylabel('Frequency')
    axes[2].set_title('Error Distribution')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_summary.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 详细评估图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    predictions = metrics['Predictions']
    labels = metrics['Labels']
    residuals = metrics['Residuals']
    
    # 预测 vs 真实
    axes[0,0].scatter(labels, predictions, alpha=0.6, s=20, color='blue')
    min_val = min(labels.min(), predictions.min())
    max_val = max(labels.max(), predictions.max())
    axes[0,0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    axes[0,0].set_xlabel('True Angles (degrees)')
    axes[0,0].set_ylabel('Predicted Angles (degrees)')
    axes[0,0].set_title(f'Predictions vs True Values\nMAE: {metrics["MAE"]:.4f}°')
    axes[0,0].grid(True, alpha=0.3)
    
    # 残差散点图
    axes[0,1].scatter(predictions, residuals, alpha=0.6, s=20, color='green')
    axes[0,1].axhline(y=0, color='red', linestyle='--', linewidth=2)
    axes[0,1].set_xlabel('Predicted Angles (degrees)')
    axes[0,1].set_ylabel('Residuals (degrees)')
    axes[0,1].set_title('Residual Plot')
    axes[0,1].grid(True, alpha=0.3)
    
    # 误差CDF
    sorted_errors = np.sort(np.abs(residuals))
    cdf_y = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
    axes[1,0].plot(sorted_errors, cdf_y, linewidth=2, color='purple')
    axes[1,0].axvline(x=metrics['MAE'], color='red', linestyle='--', linewidth=2, label=f'MAE: {metrics["MAE"]:.4f}')
    axes[1,0].set_xlabel('Absolute Error (degrees)')
    axes[1,0].set_ylabel('Cumulative Probability')
    axes[1,0].set_title('Cumulative Error Distribution')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 误差时间序列
    axes[1,1].plot(np.abs(residuals), alpha=0.7, linewidth=1, color='orange')
    axes[1,1].axhline(y=metrics['MAE'], color='red', linestyle='--', linewidth=2, label=f'MAE: {metrics["MAE"]:.4f}')
    axes[1,1].set_xlabel('Sample Index')
    axes[1,1].set_ylabel('Absolute Error (degrees)')
    axes[1,1].set_title('Error Time Series')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'detailed_evaluation.png'), dpi=300, bbox_inches='tight')
    plt.show()

    print(f"📊 图表已保存到: {save_dir}")

def plot_results_with_std_errorbars(trainer, metrics, save_dir='./results/', window_size=10):
    """绘制带有标准差误差棒的训练和评估结果"""
    os.makedirs(save_dir, exist_ok=True)

    # 1. 带误差棒的训练曲线
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    epochs = np.array(range(1, len(trainer.train_losses) + 1))

    # 计算滑动窗口的均值和标准差
    def moving_stats(data, window):
        """计算滑动窗口的均值和标准差"""
        if len(data) < window:
            return np.array(data), np.zeros_like(data)

        means = []
        stds = []
        for i in range(len(data)):
            start_idx = max(0, i - window // 2)
            end_idx = min(len(data), i + window // 2 + 1)
            window_data = data[start_idx:end_idx]
            means.append(np.mean(window_data))
            stds.append(np.std(window_data))
        return np.array(means), np.array(stds)

    # 训练损失曲线（带误差棒）
    train_means, train_stds = moving_stats(trainer.train_losses, window_size)
    val_means, val_stds = moving_stats(trainer.val_losses, window_size)

    axes[0,0].errorbar(epochs, train_means, yerr=train_stds,
                       label='Train Loss', linewidth=2, capsize=3, alpha=0.8)
    axes[0,0].errorbar(epochs, val_means, yerr=val_stds,
                       label='Val Loss', linewidth=2, capsize=3, alpha=0.8)
    axes[0,0].set_xlabel('Epoch')
    axes[0,0].set_ylabel('Loss')
    axes[0,0].set_title(f'Training and Validation Loss (±std, window={window_size})')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)

    # MAE曲线（使用实际的MAE标准差）
    if hasattr(trainer, 'val_stds') and len(trainer.val_stds) > 0:
        axes[0,1].errorbar(epochs, trainer.val_maes, yerr=trainer.val_stds,
                       label='Val MAE ± STD', linewidth=2, capsize=3, alpha=0.8, color='green')
        title_suffix = '(±batch std)'
    else:
        # 如果没有STD数据，使用滑动窗口计算
        mae_means, mae_stds = moving_stats(trainer.val_maes, window_size)
        axes[0,1].errorbar(epochs, mae_means, yerr=mae_stds,
                       label='Val MAE ± STD', linewidth=2, capsize=3, alpha=0.8, color='green')
        title_suffix = f'(±std, window={window_size})'

    # 最佳MAE标记
    best_epoch = np.argmin(trainer.val_maes) + 1
    best_mae = min(trainer.val_maes)
    axes[0,1].plot(best_epoch, best_mae, 'ro', markersize=8,
                   label=f'Best MAE: {best_mae:.4f}')

    axes[0,1].set_xlabel('Epoch')
    axes[0,1].set_ylabel('MAE')
    axes[0,1].set_title(f'Validation MAE {title_suffix}')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)

    # 预测结果散点图（带误差棒）
    predictions = metrics['Predictions']
    labels = metrics['Labels']
    residuals = metrics['Residuals']

    # 按预测值分组计算标准差
    n_bins = 20
    pred_bins = np.linspace(predictions.min(), predictions.max(), n_bins)
    bin_centers = []
    bin_means = []
    bin_stds = []

    for i in range(len(pred_bins) - 1):
        mask = (predictions >= pred_bins[i]) & (predictions < pred_bins[i+1])
        if np.sum(mask) > 0:
            bin_centers.append((pred_bins[i] + pred_bins[i+1]) / 2)
            bin_means.append(np.mean(labels[mask]))
            bin_stds.append(np.std(labels[mask]))

    bin_centers = np.array(bin_centers)
    bin_means = np.array(bin_means)
    bin_stds = np.array(bin_stds)

    # 散点图
    axes[1,0].scatter(labels, predictions, alpha=0.3, s=10, color='blue', label='Data points')

    # 误差棒
    if len(bin_centers) > 0:
        axes[1,0].errorbar(bin_means, bin_centers, xerr=bin_stds,
                          fmt='ro-', linewidth=2, capsize=5, alpha=0.8,
                          label=f'Binned mean ±std (n_bins={n_bins})')

    # 理想线
    min_val = min(labels.min(), predictions.min())
    max_val = max(labels.max(), predictions.max())
    axes[1,0].plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, alpha=0.7, label='Perfect prediction')

    axes[1,0].set_xlabel('True Angles (degrees)')
    axes[1,0].set_ylabel('Predicted Angles (degrees)')
    axes[1,0].set_title(f'Predictions vs True Values\nMAE: {metrics["MAE"]:.4f}° ± {metrics["Std_Error"]:.4f}°')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)

    # 误差分布（带统计信息）
    abs_errors = np.abs(residuals)
    axes[1,1].hist(abs_errors, bins=50, alpha=0.7, color='skyblue',
                   edgecolor='black', density=True, label='Error distribution')

    # 添加统计线
    axes[1,1].axvline(x=metrics['MAE'], color='red', linestyle='--', linewidth=2,
                      label=f'MAE: {metrics["MAE"]:.4f}°')
    axes[1,1].axvline(x=metrics['MAE'] + metrics['Std_Error'], color='orange',
                      linestyle=':', linewidth=2, label=f'MAE + σ: {metrics["MAE"] + metrics["Std_Error"]:.4f}°')
    axes[1,1].axvline(x=metrics['MAE'] - metrics['Std_Error'], color='orange',
                      linestyle=':', linewidth=2, label=f'MAE - σ: {max(0, metrics["MAE"] - metrics["Std_Error"]):.4f}°')

    # 添加正态分布拟合（如果scipy可用）
    if SCIPY_AVAILABLE:
        mu, sigma = stats.norm.fit(abs_errors)
        x_norm = np.linspace(abs_errors.min(), abs_errors.max(), 100)
        y_norm = stats.norm.pdf(x_norm, mu, sigma)
        axes[1,1].plot(x_norm, y_norm, 'g-', linewidth=2, alpha=0.8,
                       label=f'Normal fit: μ={mu:.3f}, σ={sigma:.3f}')
    else:
        # 简单的正态分布近似
        mu = np.mean(abs_errors)
        sigma = np.std(abs_errors)
        x_norm = np.linspace(abs_errors.min(), abs_errors.max(), 100)
        y_norm = (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x_norm - mu) / sigma) ** 2)
        axes[1,1].plot(x_norm, y_norm, 'g-', linewidth=2, alpha=0.8,
                       label=f'Normal approx: μ={mu:.3f}, σ={sigma:.3f}')

    axes[1,1].set_xlabel('Absolute Error (degrees)')
    axes[1,1].set_ylabel('Density')
    axes[1,1].set_title('Error Distribution with Statistics')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_summary_with_std.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 详细的误差分析图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 残差随时间变化（带滑动标准差）
    residual_means, residual_stds = moving_stats(residuals, window_size)
    sample_indices = np.arange(len(residuals))

    axes[0,0].fill_between(sample_indices, residual_means - residual_stds,
                          residual_means + residual_stds, alpha=0.3, color='lightblue',
                          label=f'±σ band (window={window_size})')
    axes[0,0].plot(sample_indices, residuals, alpha=0.6, linewidth=1, color='blue', label='Residuals')
    axes[0,0].plot(sample_indices, residual_means, color='red', linewidth=2, label='Moving mean')
    axes[0,0].axhline(y=0, color='black', linestyle='--', alpha=0.7)
    axes[0,0].set_xlabel('Sample Index')
    axes[0,0].set_ylabel('Residuals (degrees)')
    axes[0,0].set_title('Residuals Time Series with Moving Statistics')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)

    # Q-Q图检验正态性（如果scipy可用）
    if SCIPY_AVAILABLE:
        stats.probplot(residuals, dist="norm", plot=axes[0,1])
        axes[0,1].set_title('Q-Q Plot (Normality Test)')
    else:
        # 简单的Q-Q图替代
        sorted_residuals = np.sort(residuals)
        n = len(sorted_residuals)
        theoretical_quantiles = np.linspace(-3, 3, n)
        axes[0,1].scatter(theoretical_quantiles, sorted_residuals, alpha=0.6)
        axes[0,1].plot(theoretical_quantiles, theoretical_quantiles * np.std(residuals) + np.mean(residuals), 'r-')
        axes[0,1].set_xlabel('Theoretical Quantiles')
        axes[0,1].set_ylabel('Sample Quantiles')
        axes[0,1].set_title('Q-Q Plot (Approximation)')
    axes[0,1].grid(True, alpha=0.3)

    # 误差的累积分布函数
    sorted_errors = np.sort(abs_errors)
    cdf_y = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
    axes[1,0].plot(sorted_errors, cdf_y, linewidth=2, color='purple', label='Empirical CDF')

    # 添加置信区间
    confidence_levels = [0.5, 0.68, 0.95, 0.99]
    colors = ['red', 'orange', 'green', 'blue']
    for conf, color in zip(confidence_levels, colors):
        percentile_val = np.percentile(abs_errors, conf * 100)
        axes[1,0].axvline(x=percentile_val, color=color, linestyle='--',
                         label=f'{conf*100:.0f}%: {percentile_val:.3f}°')

    axes[1,0].set_xlabel('Absolute Error (degrees)')
    axes[1,0].set_ylabel('Cumulative Probability')
    axes[1,0].set_title('Cumulative Error Distribution')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)

    # 误差vs预测值（带趋势线）
    axes[1,1].scatter(predictions, abs_errors, alpha=0.5, s=15, color='green')

    # 添加趋势线
    z = np.polyfit(predictions, abs_errors, 1)
    p = np.poly1d(z)
    axes[1,1].plot(predictions, p(predictions), "r--", alpha=0.8, linewidth=2,
                   label=f'Trend: y={z[0]:.4f}x+{z[1]:.4f}')

    # 添加水平参考线
    axes[1,1].axhline(y=metrics['MAE'], color='blue', linestyle='--',
                      label=f'Overall MAE: {metrics["MAE"]:.4f}°')

    axes[1,1].set_xlabel('Predicted Angles (degrees)')
    axes[1,1].set_ylabel('Absolute Error (degrees)')
    axes[1,1].set_title('Error vs Prediction Analysis')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'detailed_error_analysis_with_std.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 打印详细的统计信息
    print("\n" + "="*80)
    print("📊 详细统计分析（包含标准差信息）")
    print("="*80)
    print(f"📈 误差统计:")
    print(f"   平均绝对误差 (MAE):        {metrics['MAE']:.6f}° ± {metrics['Std_Error']:.6f}°")
    print(f"   均方根误差 (RMSE):         {metrics['RMSE']:.6f}°")
    print(f"   最大误差:                  {metrics['Max_Error']:.6f}°")
    print(f"   误差标准差:                {metrics['Std_Error']:.6f}°")
    print(f"   误差变异系数 (CV):         {metrics['Std_Error']/metrics['MAE']:.3f}")

    print(f"\n📊 置信区间:")
    for conf in confidence_levels:
        percentile_val = np.percentile(abs_errors, conf * 100)
        print(f"   {conf*100:5.1f}% 的误差 ≤ {percentile_val:.4f}°")

    print(f"\n📋 正态性检验:")
    if SCIPY_AVAILABLE:
        shapiro_stat, shapiro_p = stats.shapiro(residuals[:min(5000, len(residuals))])  # 限制样本数量
        print(f"   Shapiro-Wilk 统计量:       {shapiro_stat:.6f}")
        print(f"   p-value:                   {shapiro_p:.6f}")
        print(f"   正态性结论:                {'接受' if shapiro_p > 0.05 else '拒绝'} (α=0.05)")
    else:
        # 简单的正态性检验替代（基于偏度和峰度）
        skewness = np.mean(((residuals - np.mean(residuals)) / np.std(residuals)) ** 3)
        kurtosis = np.mean(((residuals - np.mean(residuals)) / np.std(residuals)) ** 4) - 3
        print(f"   偏度 (Skewness):           {skewness:.6f}")
        print(f"   峰度 (Kurtosis):           {kurtosis:.6f}")
        print(f"   正态性评估:                {'近似正态' if abs(skewness) < 0.5 and abs(kurtosis) < 0.5 else '偏离正态'}")
        shapiro_stat, shapiro_p = 0.0, 1.0  # 默认值

    print("="*80)

    return {
        'error_stats': {
            'mae_with_std': f"{metrics['MAE']:.6f} ± {metrics['Std_Error']:.6f}",
            'cv': metrics['Std_Error']/metrics['MAE'],
            'percentiles': {f"{conf*100:.0f}%": np.percentile(abs_errors, conf * 100)
                           for conf in confidence_levels},
            'normality_test': {'statistic': shapiro_stat, 'p_value': shapiro_p}
        }
    }

def main():
    """主函数"""
    print("🚀 Quadriga数据集训练开始")
    print("="*70)

    # 配置参数
    config = {
        'data_path': '/home/<USER>/quadriga/UMa_v3_1000/',  # 数据路径
        'model_type': 'MLP',  # 'MLP', 'CNN', 'Classifier'
        'batch_size': 32,
        'num_epochs': 100,
        'learning_rate': 0.0001,
        'patience': 100,
        'test_split': 0.02,
        'normalize': True,
        'use_covariance': False,  # 仅CNN使用
        'save_dir': './results/',
        'model_save_path': './best_model.pth'
    }

    # 打印配置
    print("⚙️  训练配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    print()

    # 设备选择
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️  使用设备: {device}")
    if device == 'cuda':
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    print()

    try:
        # 数据预处理
        train_loader, val_loader, data_info = preprocess_quadriga_data(
            data_path=config['data_path'],
            model_type=config['model_type'],
            use_covariance=config['use_covariance'],
            normalize=config['normalize'],
            test_split=config['test_split'],
            batch_size=config['batch_size']
        )

        print(f"\n📋 数据信息:")
        print(f"   输入尺寸: {data_info['input_size']}")
        print(f"   训练样本: {data_info['n_train']}")
        print(f"   验证样本: {data_info['n_val']}")
        print(f"   标签范围: [{data_info['label_range'][0]:.1f}°, {data_info['label_range'][1]:.1f}°]")

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        print("使用模拟数据继续训练...")
        train_loader, val_loader, data_info = create_dummy_data(config['batch_size'])

    # 模型初始化
    print(f"\n🏗️  初始化{config['model_type']}模型...")

    if config['model_type'] == 'MLP':
        model = HH2ComplexMLP(
            input_size=data_info['input_size'],
            device=device,
            n_outputs=1
        )
    elif config['model_type'] == 'CNN':
        # 对于CNN，需要动态计算卷积后的尺寸
        if isinstance(data_info['input_size'], tuple) and len(data_info['input_size']) >= 2:
            # 创建临时模型计算实际输出尺寸
            temp_model = nn.Sequential(
                ComplexConv2d(1, 1, kernel_size=(3,3), stride=1, padding=2),
                ComplexBatchNorm2d(1),
                ComplexConv2d(1, 1, kernel_size=(3,3), stride=1, padding=2),
                ComplexBatchNorm2d(1),
                Flatten()
            )

            with torch.no_grad():
                sample_input = torch.randn(1, 1, *data_info['input_size'][-2:], dtype=torch.complex64)
                temp_output = temp_model(sample_input)
                actual_input_size = temp_output.shape[1]

            model = HH2ComplexCONV_1(
                input_size=actual_input_size,
                device=device,
                n_outputs=1
            )
        else:
            print("❌ CNN模式需要2D输入数据")
            return

    elif config['model_type'] == 'Classifier':
        model = HH2ComplexMLPClassifier(
            input_size=data_info['input_size'],
            device=device,
            n_outputs=3  # 假设3类分类
        )
    else:
        raise ValueError(f"不支持的模型类型: {config['model_type']}")

    print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 训练器初始化
    trainer = QuadrigaTrainer(model, device)

    # 开始训练
    print(f"\n🎯 开始训练...")
    best_mae = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['num_epochs'],
        learning_rate=config['learning_rate'],
        patience=config['patience'],
        save_path=config['model_save_path']
    )

    print(f"\n✅ 训练完成! 最佳MAE: {best_mae:.6f}")

    # 最终评估
    print(f"\n🔍 进行最终评估...")
    metrics = evaluate_model(
        model=model,
        test_loader=val_loader,
        device=device,
        model_save_path=config['model_save_path']
    )

    # 打印结果
    print_final_results(metrics)

    # 绘制结果
    print(f"\n📊 生成结果图表...")
    os.makedirs(config['save_dir'], exist_ok=True)
    plot_results(trainer, metrics, config['save_dir'])

    # 绘制带标准差误差棒的详细结果
    print(f"\n📊 生成带标准差误差棒的详细分析图表...")
    detailed_stats = plot_results_with_std_errorbars(trainer, metrics, config['save_dir'], window_size=10)

    # 保存详细结果
    results_file = os.path.join(config['save_dir'], 'training_results.npz')
    np.savez(results_file,
             predictions=metrics['Predictions'],
             labels=metrics['Labels'],
             residuals=metrics['Residuals'],
             train_losses=trainer.train_losses,
             val_losses=trainer.val_losses,
             val_maes=trainer.val_maes,
             config=config,
             detailed_stats=detailed_stats['error_stats'])

    print(f"💾 详细结果已保存到: {results_file}")
    print(f"\n🎉 所有任务完成!")

    return metrics

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)

    # 运行主程序
    try:
        final_metrics = main()
        print(f"\n🏆 最终MAE: {final_metrics['MAE']:.6f}°")
    except KeyboardInterrupt:
        print(f"\n⚠️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()