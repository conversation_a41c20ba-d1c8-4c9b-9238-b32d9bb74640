#!/usr/bin/env python3
"""
Quadriga数据集完整训练流程 - 修复版
包含数据预处理、模型训练和评估
支持方位角和仰角的同时估计
"""
import torch
import numpy as np
import os
import json
import logging
from models import HH2ComplexMLP, HH2ComplexMLPDualAngle
from data_utils import load_quadriga_data
from trainer import ImprovedQuadrigaTrainer
from evaluation import evaluate_model, print_final_results, plot_results
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import time
import os
from tqdm import tqdm
import warnings
import logging
from typing import Dict, Tuple, Optional, Union, List
import json
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 检查complexPyTorch库
try:
    from complexPyTorch.complexLayers import ComplexBatchNorm2d, ComplexConv2d, ComplexLinear
    from complexPyTorch.complexFunctions import complex_relu, complex_max_pool2d
    COMPLEX_PYTORCH_AVAILABLE = True
    logger.info("使用真实的complexPyTorch库")
except ImportError:
    logger.warning("complexPyTorch库未安装，使用模拟实现")
    COMPLEX_PYTORCH_AVAILABLE = False
    
    # 模拟复数层实现
    class ComplexLinear(nn.Module):
        def __init__(self, in_features, out_features, bias=True):
            super().__init__()
            self.real_linear = nn.Linear(in_features, out_features, bias=bias)
            self.imag_linear = nn.Linear(in_features, out_features, bias=bias)
            
            # Xavier初始化
            nn.init.xavier_uniform_(self.real_linear.weight)
            nn.init.xavier_uniform_(self.imag_linear.weight)
            
        def forward(self, x):
            if torch.is_complex(x):
                real_out = self.real_linear(x.real) - self.imag_linear(x.imag)
                imag_out = self.real_linear(x.imag) + self.imag_linear(x.real)
                return torch.complex(real_out, imag_out)
            else:
                return torch.complex(self.real_linear(x), self.imag_linear(x))
    
    class ComplexConv2d(nn.Module):
        def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0):
            super().__init__()
            self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            
        def forward(self, x):
            real_out = self.real_conv(x.real) - self.imag_conv(x.imag)
            imag_out = self.real_conv(x.imag) + self.imag_conv(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexBatchNorm2d(nn.Module):
        def __init__(self, num_features):
            super().__init__()
            self.real_bn = nn.BatchNorm2d(num_features)
            self.imag_bn = nn.BatchNorm2d(num_features)
            
        def forward(self, x):
            return torch.complex(self.real_bn(x.real), self.imag_bn(x.imag))
    
    def complex_relu(x):
        return torch.complex(torch.relu(x.real), torch.relu(x.imag))

# 安全的torch.load函数
def safe_torch_load(path: str, map_location=None) -> Dict:
    """安全加载torch模型，兼容PyTorch 2.6+"""
    try:
        return torch.load(path, map_location=map_location, weights_only=False)
    except Exception as e:
        logger.warning(f"标准加载失败: {e}")
        try:
            import torch.serialization
            safe_globals = [
                'numpy._core.multiarray.scalar',
                'numpy.core.multiarray.scalar', 
                'numpy.dtype',
                'collections.OrderedDict',
                'builtins.dict',
                'builtins.list'
            ]
            with torch.serialization.safe_globals(safe_globals):
                return torch.load(path, map_location=map_location, weights_only=True)
        except Exception as e2:
            logger.error(f"安全加载也失败: {e2}")
            raise e2

# 角度计算工具函数
def calculate_angles_from_positions(positions: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """从3D位置坐标计算方位角和仰角"""
    x, y, z = positions[:, 0], positions[:, 1], positions[:, 2]
    horizontal_distance = np.sqrt(x**2 + y**2)
    azimuth_angles = np.arctan2(y, x) * 180 / np.pi
    elevation_angles = np.arctan2(z, horizontal_distance) * 180 / np.pi
    return azimuth_angles, elevation_angles

def angle_distance(pred_angles: np.ndarray, true_angles: np.ndarray, 
                  angle_type: str = 'azimuth') -> np.ndarray:
    """计算角度误差，考虑角度的周期性"""
    diff = pred_angles - true_angles
    
    if angle_type == 'azimuth':
        diff = ((diff + 180) % 360) - 180
    elif angle_type == 'elevation':
        diff = np.clip(diff, -180, 180)
    
    return np.abs(diff)

# 复数激活函数
class ComplexActivation(nn.Module):
    """复数激活函数"""
    def __init__(self, activation_type='relu'):
        super().__init__()
        self.activation_type = activation_type
        
    def forward(self, x):
        if self.activation_type == 'relu':
            return torch.complex(torch.relu(x.real), torch.relu(x.imag))
        elif self.activation_type == 'leaky_relu':
            return torch.complex(torch.leaky_relu(x.real), torch.leaky_relu(x.imag))
        elif self.activation_type == 'tanh':
            return torch.complex(torch.tanh(x.real), torch.tanh(x.imag))
        else:
            return x

# 单角度MLP模型
class HH2ComplexMLP(nn.Module):
    """复数MLP模型 - 单角度"""
    def __init__(self, input_size: int, device: str = 'cpu', 
                 hidden_dims: List[int] = None, dropout_rate: float = 0.2):
        super(HH2ComplexMLP, self).__init__()
        self.device = device
        self.input_size = input_size
        
        if hidden_dims is None:
            hidden_dims = [1024, 512, 256, 128]
        
        # 构建复数层
        layers = []
        prev_dim = input_size
        
        for hidden_dim in hidden_dims:
            layers.append(ComplexLinear(prev_dim, hidden_dim))
            layers.append(ComplexActivation('relu'))
            layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        self.complex_layers = nn.Sequential(*layers)
        
        # 实数输出层
        self.output_layer = nn.Sequential(
            nn.Linear(prev_dim * 2, prev_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(prev_dim, 1)
        )
        
        self._initialize_weights()
        
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            # 设置日志
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
            logger = logging.getLogger(__name__)
    def main():
        """主训练函数"""
        # 训练配置
        config = {
            'data_path': '/home/<USER>/quadriga/inf_v0/',
            'batch_size': 64,
            'num_epochs': 200,
            'learning_rate': 0.001,
            'dual_angle': True,
            'patience': 20,
            'test_split': 0.02,
            'normalize': True,
            'use_covariance': False,
            'save_dir': './results/',
            'model_save_path': './best_model_improved.pth',
            'use_scheduler': True,
            'scheduler_type': 'plateau',
            'hidden_dims': [1024, 512, 256, 128],
            'dropout_rate': 0.2,
            'activation': 'relu'
        }

        # 打印配置
        print("⚙️  训练配置:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        print()

        # 设备选择
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"🖥️  使用设备: {device}")
        if device == 'cuda':
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
            print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
        print()

        # 数据加载
        print(f"📂 加载数据...")
        train_loader, val_loader, data_info = load_quadriga_data(
            data_path=config['data_path'],
            batch_size=config['batch_size'],
            test_split=config['test_split'],
            normalize=config['normalize'],
            dual_angle=config['dual_angle'],
            use_covariance=config['use_covariance']
        )

        # 模型初始化
        print(f"\n🏗️  初始化模型...")
        if config['dual_angle']:
            model = HH2ComplexMLPDualAngle(
                input_size=data_info['input_size'],
                device=device,
                hidden_dims=config['hidden_dims'],
                dropout_rate=config['dropout_rate'],
                activation=config['activation']
            )
        else:
            model = HH2ComplexMLP(
                input_size=data_info['input_size'],
                device=device,
                hidden_dims=config['hidden_dims'],
                dropout_rate=config['dropout_rate'],
                activation=config['activation']
            )

        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

        # 训练器初始化
        trainer = ImprovedQuadrigaTrainer(model, device, dual_angle=config['dual_angle'])

        # 开始训练
        print(f"\n🎯 开始训练...")
        best_mae = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=config['num_epochs'],
            learning_rate=config['learning_rate'],
            patience=config['patience'],
            save_path=config['model_save_path'],
            use_scheduler=config['use_scheduler'],
            scheduler_type=config['scheduler_type']
        )

        print(f"\n✅ 训练完成! 最佳MAE: {best_mae:.6f}")

        # 最终评估
        print(f"\n🔍 进行最终评估...")
        metrics = evaluate_model(
            model=model,
            test_loader=val_loader,
            device=device,
            dual_angle=config['dual_angle'],
            model_save_path=config['model_save_path']
        )

        # 打印结果
        print_final_results(metrics, dual_angle=config['dual_angle'])

        # 绘制结果
        print(f"\n📊 生成结果图表...")
        os.makedirs(config['save_dir'], exist_ok=True)
        plot_results(trainer, metrics, config['save_dir'], dual_angle=config['dual_angle'])

        # 保存详细结果
        results_file = os.path.join(config['save_dir'], 'training_results_improved.npz')
        save_dict = {
            'train_losses': trainer.train_losses,
            'val_losses': trainer.val_losses,
            'val_maes': trainer.val_maes,
            'learning_rates': trainer.learning_rates,
            'best_metrics': trainer.best_metrics
        }
        
        if config['dual_angle']:
            save_dict.update({
                'predictions': metrics['predictions'],
                'labels': metrics['labels'],
                'azimuth_errors': metrics['azimuth_errors'],
                'elevation_errors': metrics['elevation_errors']
            })
        else:
            save_dict.update({
                'predictions': metrics['Predictions'],
                'labels': metrics['Labels'],
                'residuals': metrics['Residuals']
            })

        np.savez(results_file, **save_dict)
        
        # 保存配置
        config_file = os.path.join(config['save_dir'], 'config.json')
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)

        print(f"💾 详细结果已保存到: {results_file}")
        print(f"⚙️  配置已保存到: {config_file}")
        print(f"\n🎉 所有任务完成!")

        return metrics

    if __name__ == "__main__":
        # 设置随机种子
        torch.manual_seed(42)
        np.random.seed(42)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(42)
            torch.cuda.manual_seed_all(42)
        
        # 运行主函数
        try:
            metrics = main()
            logger.info("🏆 训练成功完成!")
        except KeyboardInterrupt:
            logger.warning("⚠️  训练被用户中断")
        except Exception as e:
            logger.error(f"❌ 训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
