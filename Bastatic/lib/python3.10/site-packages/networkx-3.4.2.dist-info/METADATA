Metadata-Version: 2.1
Name: networkx
Version: 3.4.2
Summary: Python package for creating and manipulating graphs and networks
Author-email: <PERSON><PERSON> <<EMAIL>>
Maintainer-email: NetworkX Developers <<EMAIL>>
Project-URL: Homepage, https://networkx.org/
Project-URL: Bug Tracker, https://github.com/networkx/networkx/issues
Project-URL: Documentation, https://networkx.org/documentation/stable/
Project-URL: Source Code, https://github.com/networkx/networkx
Keywords: Networks,Graph Theory,Mathematics,network,graph,discrete mathematics,math
Platform: Linux
Platform: Mac OSX
Platform: Windows
Platform: Unix
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Bio-Informatics
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Scientific/Engineering :: Physics
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Provides-Extra: default
Requires-Dist: numpy >=1.24 ; extra == 'default'
Requires-Dist: scipy !=1.11.0,!=1.11.1,>=1.10 ; extra == 'default'
Requires-Dist: matplotlib >=3.7 ; extra == 'default'
Requires-Dist: pandas >=2.0 ; extra == 'default'
Provides-Extra: developer
Requires-Dist: changelist ==0.5 ; extra == 'developer'
Requires-Dist: pre-commit >=3.2 ; extra == 'developer'
Requires-Dist: mypy >=1.1 ; extra == 'developer'
Requires-Dist: rtoml ; extra == 'developer'
Provides-Extra: doc
Requires-Dist: sphinx >=7.3 ; extra == 'doc'
Requires-Dist: pydata-sphinx-theme >=0.15 ; extra == 'doc'
Requires-Dist: sphinx-gallery >=0.16 ; extra == 'doc'
Requires-Dist: numpydoc >=1.8.0 ; extra == 'doc'
Requires-Dist: pillow >=9.4 ; extra == 'doc'
Requires-Dist: texext >=0.6.7 ; extra == 'doc'
Requires-Dist: myst-nb >=1.1 ; extra == 'doc'
Requires-Dist: intersphinx-registry ; extra == 'doc'
Provides-Extra: example
Requires-Dist: osmnx >=1.9 ; extra == 'example'
Requires-Dist: momepy >=0.7.2 ; extra == 'example'
Requires-Dist: contextily >=1.6 ; extra == 'example'
Requires-Dist: seaborn >=0.13 ; extra == 'example'
Requires-Dist: cairocffi >=1.7 ; extra == 'example'
Requires-Dist: igraph >=0.11 ; extra == 'example'
Requires-Dist: scikit-learn >=1.5 ; extra == 'example'
Provides-Extra: extra
Requires-Dist: lxml >=4.6 ; extra == 'extra'
Requires-Dist: pygraphviz >=1.14 ; extra == 'extra'
Requires-Dist: pydot >=3.0.1 ; extra == 'extra'
Requires-Dist: sympy >=1.10 ; extra == 'extra'
Provides-Extra: test
Requires-Dist: pytest >=7.2 ; extra == 'test'
Requires-Dist: pytest-cov >=4.0 ; extra == 'test'

NetworkX
========


.. image::
    https://github.com/networkx/networkx/workflows/test/badge.svg?branch=main
    :target: https://github.com/networkx/networkx/actions?query=workflow%3Atest

.. image::
    https://codecov.io/gh/networkx/networkx/branch/main/graph/badge.svg?
    :target: https://app.codecov.io/gh/networkx/networkx/branch/main

.. image::
    https://img.shields.io/pypi/v/networkx.svg?
    :target: https://pypi.python.org/pypi/networkx

.. image::
    https://img.shields.io/pypi/l/networkx.svg?
    :target: https://github.com/networkx/networkx/blob/main/LICENSE.txt

.. image::
    https://img.shields.io/pypi/pyversions/networkx.svg?
    :target: https://pypi.python.org/pypi/networkx

.. image::
    https://img.shields.io/github/labels/networkx/networkx/good%20first%20issue?color=green&label=contribute
    :target: https://github.com/networkx/networkx/contribute


NetworkX is a Python package for the creation, manipulation,
and study of the structure, dynamics, and functions
of complex networks.

- **Website (including documentation):** https://networkx.org
- **Mailing list:** https://groups.google.com/forum/#!forum/networkx-discuss
- **Source:** https://github.com/networkx/networkx
- **Bug reports:** https://github.com/networkx/networkx/issues
- **Report a security vulnerability:** https://tidelift.com/security
- **Tutorial:** https://networkx.org/documentation/latest/tutorial.html
- **GitHub Discussions:** https://github.com/networkx/networkx/discussions
- **Discord (Scientific Python) invite link:** https://discord.com/invite/vur45CbwMz
- **NetworkX meetings calendar (open to all):** https://scientific-python.org/calendars/networkx.ics

Simple example
--------------

Find the shortest path between two nodes in an undirected graph:

.. code:: pycon

    >>> import networkx as nx
    >>> G = nx.Graph()
    >>> G.add_edge("A", "B", weight=4)
    >>> G.add_edge("B", "D", weight=2)
    >>> G.add_edge("A", "C", weight=3)
    >>> G.add_edge("C", "D", weight=4)
    >>> nx.shortest_path(G, "A", "D", weight="weight")
    ['A', 'B', 'D']

Install
-------

Install the latest released version of NetworkX:

.. code:: shell

    $ pip install networkx

Install with all optional dependencies:

.. code:: shell

    $ pip install networkx[default]

For additional details,
please see the `installation guide <https://networkx.org/documentation/stable/install.html>`_.

Bugs
----

Please report any bugs that you find `here <https://github.com/networkx/networkx/issues>`_.
Or, even better, fork the repository on `GitHub <https://github.com/networkx/networkx>`_
and create a pull request (PR). We welcome all changes, big or small, and we
will help you make the PR if you are new to `git` (just ask on the issue and/or
see the `contributor guide <https://networkx.org/documentation/latest/developer/contribute.html>`_).

License
-------

Released under the `3-Clause BSD license <https://github.com/networkx/networkx/blob/main/LICENSE.txt>`_::

    Copyright (C) 2004-2024 NetworkX Developers
    Aric Hagberg <<EMAIL>>
    Dan Schult <<EMAIL>>
    Pieter Swart <<EMAIL>>
