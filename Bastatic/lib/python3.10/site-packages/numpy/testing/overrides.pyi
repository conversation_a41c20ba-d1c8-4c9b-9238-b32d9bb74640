from collections.abc import Callable, <PERSON><PERSON><PERSON>
from typing import Any

from typing_extensions import TypeIs

import numpy as np

def get_overridable_numpy_ufuncs() -> set[np.ufunc]: ...
def get_overridable_numpy_array_functions() -> set[Callable[..., Any]]: ...
def allows_array_ufunc_override(func: object) -> TypeIs[np.ufunc]: ...
def allows_array_function_override(func: Hashable) -> bool: ...
